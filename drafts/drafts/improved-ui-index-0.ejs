<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Aetheris' URL Shortener</title>
    <link rel="icon" type="image/png" href="/logo.png">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --success: #22c55e;
            --background: #ffffff;
            --foreground: #0f172a;
            --muted: #f1f5f9;
            --muted-foreground: #64748b;
            --border: #e2e8f0;
        }

        [data-theme="dark"] {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --success: #22c55e;
            --background: #0f172a;
            --foreground: #f8fafc;
            --muted: #1e293b;
            --muted-foreground: #94a3b8;
            --border: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background);
            color: var(--foreground);
            line-height: 1.5;
            transition: background-color 0.3s, color 0.3s;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--muted);
            border: 1px solid var(--border);
            color: var(--foreground);
            padding: 0.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        .theme-toggle:hover {
            background: var(--primary);
            color: white;
        }

        .hero {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeIn 0.5s ease-out;
        }

        .hero h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(to right, var(--primary), var(--success));
            -webkit-background-clip: text;
            color: transparent;
        }

        .hero p {
            color: var(--muted-foreground);
            max-width: 600px;
            margin: 0 auto;
        }

        .url-form {
            background: var(--muted);
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .form-group {
            display: flex;
            gap: 1rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .input-group {
            flex: 1;
            position: relative;
        }

        input[type="url"] {
            width: 100%;
            padding: 1rem 1.5rem;
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            background: var(--background);
            color: var(--foreground);
            font-size: 1rem;
            transition: all 0.3s;
        }

        input[type="url"]:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px var(--primary-dark);
        }

        .submit-btn {
            padding: 1rem 2rem;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .submit-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .urls-table {
            background: var(--muted);
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }

        .table th {
            background: var(--background);
            font-weight: 600;
            color: var(--foreground);
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table a {
            color: var(--primary);
            text-decoration: none;
            transition: color 0.3s;
        }

        .table a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .clicks-badge {
            background: var(--success);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .form-group {
                flex-direction: column;
            }

            .submit-btn {
                width: 100%;
            }

            .table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .table th,
            .table td {
                padding: 0.75rem;
            }
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--muted-foreground);
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()" aria-label="Toggle theme">
        🌓
    </button>

    <div class="container">
        <div class="hero">
            <h1>URL Shortener</h1>
            <p>Transform your long URLs into short, memorable links that are easy to share and track.</p>
        </div>

        <form action="/shortUrls" method="POST" class="url-form">
            <div class="form-group">
                <div class="input-group">
                    <input required 
                           placeholder="Enter your URL to shorten (e.g., https://example.com)" 
                           type="url" 
                           name="fullUrl" 
                           id="fullUrl"
                           aria-label="URL to shorten">
                </div>
                <button class="submit-btn" type="submit">Shorten URL</button>
            </div>
        </form>

        <div class="urls-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Original URL</th>
                        <th>Short URL</th>
                        <th>Clicks</th>
                    </tr>
                </thead>
                <tbody>
                    <% if (shortUrls && shortUrls.length > 0) { %>
                        <% shortUrls.forEach(shortUrl => { %>
                            <tr>
                                <td>
                                    <a href="<%= shortUrl.full %>" target="_blank" rel="noopener noreferrer">
                                        <%= shortUrl.full.length > 50 ? shortUrl.full.substring(0, 50) + '...' : shortUrl.full %>
                                    </a>
                                </td>
                                <td>
                                    <a href="/<%= shortUrl.short %>" target="_blank" rel="noopener noreferrer">
                                        /<%= shortUrl.short %>
                                    </a>
                                </td>
                                <td>
                                    <span class="clicks-badge"><%= shortUrl.clicks %></span>
                                </td>
                            </tr>
                        <% }) %>
                    <% } else { %>
                        <tr>
                            <td colspan="3">
                                <div class="empty-state">
                                    No shortened URLs yet. Create your first one above!
                                </div>
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // Set initial theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.body.setAttribute('data-theme', savedTheme);

        // Add loading state to form
        document.querySelector('form').addEventListener('submit', function(e) {
            const button = this.querySelector('button');
            button.disabled = true;
            button.innerHTML = 'Shortening...';
        });

        // Truncate long URLs in the table
        document.querySelectorAll('.table a').forEach(link => {
            if (link.innerText.length > 50) {
                link.setAttribute('title', link.innerText);
            }
        });
    </script>
</body>
</html>