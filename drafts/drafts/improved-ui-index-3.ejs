<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Aetheris' URL Shortener</title>
    <link rel="icon" type="image/png" href="/logo.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --background: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --table-header: #f8fafc;
            --badge-bg: #e0e7ff;
            --badge-color: #4338ca;
            --shadow-color: rgb(0 0 0 / 0.1);
        }

        [data-theme="dark"] {
            --primary-color: #60a5fa;
            --primary-hover: #3b82f6;
            --background: #0f172a;
            --card-background: #1e293b;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --border-color: #334155;
            --table-header: #1e293b;
            --badge-bg: #1e40af;
            --badge-color: #93c5fd;
            --shadow-color: rgb(0 0 0 / 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 1rem;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3rem;
        }

        .title-section {
            text-align: left;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .theme-toggle {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            padding: 0.5rem;
            border-radius: 2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            border-color: var(--primary-color);
        }

        .theme-toggle svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: var(--text-primary);
        }

        .url-form {
            background: var(--card-background);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px var(--shadow-color);
            margin-bottom: 3rem;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-group {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .input-wrapper {
            flex: 1;
            min-width: 200px;
        }

        input[type="url"] {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--card-background);
            color: var(--text-primary);
        }

        input[type="url"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        button {
            padding: 0.75rem 2rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        .urls-table {
            background: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px var(--shadow-color);
            overflow: hidden;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            transition: border-color 0.3s ease;
        }

        th {
            background-color: var(--table-header);
            font-weight: 600;
            color: var(--text-secondary);
        }

        td a {
            color: var(--primary-color);
            text-decoration: none;
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        td a:hover {
            text-decoration: underline;
        }

        .clicks-badge {
            background-color: var(--badge-bg);
            color: var(--badge-color);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .title-section {
                text-align: center;
            }

            .header h1 {
                font-size: 2rem;
            }

            .url-form {
                padding: 1.5rem;
            }

            th, td {
                padding: 0.75rem;
            }

            td a {
                max-width: 200px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.75rem;
            }

            .urls-table {
                display: block;
                overflow-x: auto;
            }

            td a {
                max-width: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title-section">
                <h1>URL Shortener</h1>
                <p>Simplify your links with just one click</p>
            </div>
            <button class="theme-toggle" onclick="toggleTheme()" aria-label="Toggle theme">
                <svg class="sun-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                </svg>
            </button>
        </div>

        <div class="url-form">
            <form action="/shortUrls" method="POST">
                <div class="form-group">
                    <div class="input-wrapper">
                        <input required 
                               placeholder="Enter your URL to shorten (e.g., https://example.com)" 
                               type="url" 
                               name="fullUrl" 
                               id="fullUrl">
                    </div>
                    <button type="submit">Shorten URL</button>
                </div>
            </form>
        </div>

        <div class="urls-table">
            <table>
                <thead>
                    <tr>
                        <th>Original URL</th>
                        <th>Short URL</th>
                        <th>Clicks</th>
                    </tr>
                </thead>
                <tbody>
                    <% shortUrls.forEach(shortUrl => { %>
                        <tr>
                            <td><a href="<%= shortUrl.full %>" target="_blank"><%= shortUrl.full %></a></td>
                            <td><a href="/<%= shortUrl.short %>">/<%= shortUrl.short %></a></td>
                            <td><span class="clicks-badge"><%= shortUrl.clicks %></span></td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Update theme toggle icon
            const themeToggle = document.querySelector('.theme-toggle');
            updateThemeIcon(newTheme);
        }

        // Update theme icon based on current theme
        function updateThemeIcon(theme) {
            const themeToggle = document.querySelector('.theme-toggle');
            if (theme === 'dark') {
                themeToggle.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
                    </svg>`;
            } else {
                themeToggle.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                    </svg>`;
            }
        }

        // Initialize theme from localStorage or system preference
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const theme = savedTheme || (prefersDark ? 'dark' : 'light');
            
            document.body.setAttribute('data-theme', theme);
            updateThemeIcon(theme);
        }

        // Call initialize theme on page load
        initializeTheme();
    </script>
</body>
</html>