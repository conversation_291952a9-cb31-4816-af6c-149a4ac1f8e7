<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Aetheris' URL Shortener</title>
    <link rel="icon" type="image/png" href="/logo.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --card-bg: rgba(255, 255, 255, 0.95);
            --primary-color: #6366f1;
            --text-primary: #1f2937;
            --text-secondary: #4b5563;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .card {
            background: var(--card-bg);
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
                       0 10px 10px -5px rgba(0, 0, 0, 0.04);
            backdrop-filter: blur(10px);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .url-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        @media (max-width: 640px) {
            .url-form {
                flex-direction: column;
            }
        }

        .input-group {
            flex: 1;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.2s;
            outline: none;
        }

        .form-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                       0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .table-container {
            overflow-x: auto;
            border-radius: 0.5rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }

        th {
            background: #f9fafb;
            padding: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        td {
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
            color: var(--text-secondary);
        }

        tr:hover td {
            background: #f9fafb;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.2s;
        }

        a:hover {
            color: #4f46e5;
        }

        .clicks-badge {
            background: #e0e7ff;
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            td, th {
                padding: 0.75rem;
            }

            .card {
                padding: 1.5rem;
            }
        }

        .animate-fade {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header animate-fade">
            <h1>URL Shortener</h1>
            <p>Shorten your URLs in seconds</p>
        </header>

        <div class="card animate-fade">
            <form action="/shortUrls" method="POST" class="url-form">
                <div class="input-group">
                    <input 
                        required 
                        placeholder="Enter your URL (e.g., https://example.com)" 
                        type="url" 
                        name="fullUrl" 
                        id="fullUrl" 
                        class="form-input"
                        autocomplete="off"
                    >
                </div>
                <button class="btn" type="submit">
                    Shorten URL
                </button>
            </form>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Original URL</th>
                            <th>Short URL</th>
                            <th>Clicks</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% shortUrls.forEach(shortUrl => { %>
                            <tr>
                                <td>
                                    <a href="<%= shortUrl.full %>" target="_blank" title="<%= shortUrl.full %>">
                                        <%= shortUrl.full.length > 50 ? shortUrl.full.substring(0, 50) + '...' : shortUrl.full %>
                                    </a>
                                </td>
                                <td>
                                    <a href="/<%= shortUrl.short %>" target="_blank">
                                        /<%= shortUrl.short %>
                                    </a>
                                </td>
                                <td>
                                    <span class="clicks-badge"><%= shortUrl.clicks %></span>
                                </td>
                            </tr>
                        <% }) %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Add smooth animation when new URLs are added
        document.querySelector('form').addEventListener('submit', function() {
            this.querySelector('button').textContent = 'Shortening...';
            this.querySelector('button').disabled = true;
        });

        // Truncate long URLs in the table
        document.querySelectorAll('td a').forEach(link => {
            if (link.textContent.length > 50) {
                link.textContent = link.textContent.substring(0, 50) + '...';
            }
        });
    </script>
</body>
</html>