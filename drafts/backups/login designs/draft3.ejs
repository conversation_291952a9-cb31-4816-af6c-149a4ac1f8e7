<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login To Begin</title>
  <link rel="icon" type="image/png" href="/logo.png">
  <link rel="stylesheet" href="https://unpkg.com/lucide-static@0.321.0/font/lucide.css">
  <style>
    /* Theme Variables */
    :root {
      --background: #ffffff;
      --foreground: #1a1a1a;
      --primary: #4361ee;
      --secondary: #3f37c9;
      --muted: #64748b;
      --border: #e2e8f0;
      --card-bg: #ffffff;
      --input-bg: #f8fafc;
      --error: #ef4444;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --warning-bg: #fee2e2;
      --warning-text: #0c0c0c;
      
      /* Gradients */
      --gradient-primary: linear-gradient(135deg, #4361ee, #3f37c9);
      --gradient-hover: linear-gradient(135deg, #3f37c9, #4361ee);
    }

    [data-theme="dark"] {
      --background: #0f172a;
      --foreground: #f8fafc;
      --primary: #60a5fa;
      --secondary: #818cf8;
      --muted: #94a3b8;
      --border: #1e293b;
      --card-bg: #1e293b;
      --input-bg: #0f172a;
      --error: #f87171;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
      --warning-bg: #7f1d1d;
      --warning-text: #fecaca;
    }

    /* Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      transition: background-color 0.3s, color 0.3s, border-color 0.3s;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: var(--background);
      color: var(--foreground);
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* NoScript Warning */
    .noscript-warning {
      background-color: var(--warning-bg);
      color: var(--warning-text);
      text-align: center;
      padding: 1rem;
      font-size: 0.875rem;
    }

    /* Main Content */
    main {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      position: relative;
    }

    /* Login Form Container */
    .form-container {
      background-color: var(--card-bg);
      padding: 2.5rem;
      border-radius: 1rem;
      box-shadow: var(--shadow);
      width: 100%;
      max-width: 400px;
      animation: fadeInUp 0.5s ease-out;
    }

    h1 {
      font-size: 2rem;
      margin-bottom: 2rem;
      text-align: center;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }

    /* Form Styles */
    .form-group {
      margin-bottom: 1.5rem;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: var(--foreground);
      font-weight: 500;
    }

    .input-wrapper {
      position: relative;
    }

    input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid var(--border);
      border-radius: 0.5rem;
      background-color: var(--input-bg);
      color: var(--foreground);
      font-size: 1rem;
      transition: all 0.3s ease;
    }

    input:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    }

    /* Password Toggle */
    .password-toggle {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: var(--muted);
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 0.25rem;
      transition: all 0.3s ease;
    }

    .password-toggle:hover {
      color: var(--primary);
      background-color: rgba(67, 97, 238, 0.1);
    }

    /* Button Styles */
    button[type="submit"] {
      width: 100%;
      padding: 0.875rem;
      background: var(--gradient-primary);
      color: white;
      border: none;
      border-radius: 0.5rem;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: transform 0.2s, box-shadow 0.2s;
      margin-top: 1rem;
    }

    button[type="submit"]:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
      background: var(--gradient-hover);
    }

    /* Error Message */
    .error-message {
      position: relative;
      z-index: 2;
      background-color: var(--warning-bg);
      color: var(--warning-text);
      padding: 0.75rem 1rem;
      border-radius: 0.5rem;
      margin-bottom: 1.5rem;
      font-size: 0.875rem;
      animation: shake 0.5s ease-out;
    }

    /* Sign Up Link */
    .signup-link {
      text-align: center;
      margin-top: 1.5rem;
      color: var(--muted);
    }
    .signup-link a {
      color: var(--primary);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s;
    }

    .signup-link a:hover {
      color: var(--secondary);
    }

    /* Theme Toggle */
    .theme-toggle {
      position: fixed;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      color: var(--foreground);
      padding: 0.5rem;
      cursor: pointer;
      border-radius: 0.5rem;
      transition: background-color 0.2s;
      z-index: 100;
    }

    .theme-toggle:hover {
      background-color: var(--border);
    }

    /* Footer */
    footer {
      text-align: center;
      padding: 2rem;
      border-top: 1px solid var(--border);
      color: var(--muted);
      font-size: 0.875rem;
    }

    footer a {
      color: var(--muted);
      text-decoration: none;
      transition: color 0.2s;
    }

    footer a:hover {
      color: var(--primary);
    }

    /* Animations */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }

    /* Responsive Design */
    @media (max-width: 640px) {
      main {
        padding: 1.5rem;
      }

      .form-container {
        padding: 1.5rem;
      }
    }

    @media (prefers-reduced-motion: reduce) {
      * {
        animation: none !important;
        transition: none !important;
      }
    }
  </style>
</head>
<body>
  <noscript>
    <div class="noscript-warning">
      You must enable JavaScript to be able to use the website.
    </div>
  </noscript>

  <button class="theme-toggle" aria-label="Toggle theme">
    🌓
  </button>

  <main>
    <div class="form-container">
      <h1>Welcome Back</h1>
      
      <% if (error) { %>
        <div class="error-message">
          <span class="error-icon">⚠️</span>
          <%= error %>
        </div>
      <% } %>

      <form action="/login" method="POST">
        <div class="form-group">
          <label for="email">Email</label>
          <div class="input-wrapper">
            <input 
              type="email" 
              id="email" 
              name="email" 
              required 
              autocomplete="email"
              placeholder="Enter your email"
            >
          </div>
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <div class="input-wrapper">
            <input 
              type="password" 
              id="password" 
              name="password" 
              required 
              autocomplete="current-password"
              placeholder="Enter your password"
            >
            <button type="button" class="password-toggle" aria-label="Toggle password visibility">
              <span class="toggle-icon">👁‍🗨</span>
            </button>
          </div>
        </div>

        <button type="submit">
          <i class="lucide-log-in"></i>
          Login
        </button>

        <p class="signup-link">
          Don't have an account? 
          <a href="/signup">Sign up here</a>
        </p>
      </form>
    </div>
  </main>

  <footer>
    <p>
      ©️ 2025 copyright • 
      <a href="https://github.com/dohoudaniel" target="_blank" rel="noopener noreferrer">Daniel Favour Dohou</a>
      • All Rights Reserved
    </p>
  </footer>

  <script>
    // Theme Toggle
    const themeToggle = document.querySelector('.theme-toggle');
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (savedTheme) {
      document.documentElement.setAttribute('data-theme', savedTheme);
    } else if (prefersDark) {
      document.documentElement.setAttribute('data-theme', 'dark');
    }
    themeToggle.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    });

    // Password Toggle
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.querySelector('.password-toggle');
    const toggleIcon = passwordToggle.querySelector('.toggle-icon');
    passwordToggle.addEventListener('click', () => {
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.textContent = '🙈';
      } else {
        passwordInput.type = 'password';
        toggleIcon.textContent = '👁‍🗨';
      }
    });

    // JavaScript check
    if (!document.cookie.includes('js_enabled=true')) {
      document.cookie = "js_enabled=true; path=/";
      location.reload();
    }
  </script>
</body>
</html>
