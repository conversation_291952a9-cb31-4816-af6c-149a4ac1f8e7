{"name": "url_shortener", "version": "1.0.0", "description": "A URL shortener built by <PERSON> for the Future Interns internship program.", "homepage": "https://github.com/dohoudaniel/FUTURE_FS_03#readme", "bugs": {"url": "https://github.com/dohoudaniel/FUTURE_FS_03/issues"}, "repository": {"type": "git", "url": "git+https://github.com/dohoudaniel/FUTURE_FS_03.git"}, "license": "MIT", "author": "<PERSON>", "type": "commonjs", "main": "index.js", "scripts": {"start": "node server.js", "devStart": "nodemon server.js", "build": "echo 'Build completed'"}, "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-flash": "^0.0.2", "express-session": "^1.18.1", "mongoose": "^8.10.0", "serve-favicon": "^2.5.0", "serverless-http": "^3.2.0", "shortid": "^2.2.17"}, "devDependencies": {"nodemon": "^3.1.9"}}