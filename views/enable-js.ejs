<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JavaScript Required</title>
  <link rel="icon" type="image/png" href="/logo.png">
  <style>
    /* Theme Variables */
    :root {
      --background: #ffffff;
      --foreground: #1a1a1a;
      --primary: #4361ee;
      --secondary: #3f37c9;
      --muted: #64748b;
      --border: #e2e8f0;
      --card-bg: #ffffff;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      
      /* Gradients */
      --gradient-primary: linear-gradient(135deg, #4361ee, #3f37c9);
      --gradient-hover: linear-gradient(135deg, #3f37c9, #4361ee);
    }

    [data-theme="dark"] {
      --background: #0f172a;
      --foreground: #f8fafc;
      --primary: #60a5fa;
      --secondary: #818cf8;
      --muted: #94a3b8;
      --border: #1e293b;
      --card-bg: #1e293b;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    }

    /* Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      transition: background-color 0.3s, color 0.3s, border-color 0.3s;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: var(--background);
      color: var(--foreground);
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* Main Content */
    main {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      text-align: center;
    }

    .content-container {
      background-color: var(--card-bg);
      padding: 2.5rem;
      border-radius: 1rem;
      box-shadow: var(--shadow);
      width: 100%;
      max-width: 600px;
      animation: fadeInUp 0.5s ease-out;
    }

    h1 {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      display: inline-block;
    }

    .icon-container {
      margin-bottom: 2rem;
    }

    .js-icon {
      font-size: 4rem;
      color: var(--primary);
    }

    .message {
      font-size: 1.125rem;
      color: var(--foreground);
      margin-bottom: 2rem;
    }

    .help-text {
      color: var(--muted);
      font-size: 0.875rem;
      margin-bottom: 1.5rem;
    }

    .help-link {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--primary);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }

    .help-link:hover {
      background-color: var(--border);
      color: var(--secondary);
    }

    /* Theme Toggle */
    .theme-toggle {
      position: fixed;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      color: var(--foreground);
      padding: 0.5rem;
      cursor: pointer;
      border-radius: 0.5rem;
      transition: background-color 0.2s;
      z-index: 100;
      font-size: 1.25rem;
    }

    .theme-toggle:hover {
      background-color: var(--border);
    }

    /* Footer */
    footer {
      text-align: center;
      padding: 2rem;
      border-top: 1px solid var(--border);
      color: var(--muted);
      font-size: 0.875rem;
    }

    footer a {
      color: var(--muted);
      text-decoration: none;
      transition: color 0.2s;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    footer a:hover {
      color: var(--primary);
    }

    /* Animations */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Responsive Design */
    @media (max-width: 640px) {
      main {
        padding: 1.5rem;
      }

      .content-container {
        padding: 1.5rem;
      }

      h1 {
        font-size: 2rem;
      }

      .message {
        font-size: 1rem;
      }
    }

    @media (prefers-reduced-motion: reduce) {
      * {
        animation: none !important;
        transition: none !important;
      }
    }
  </style>
</head>
<body>
  <button class="theme-toggle" aria-label="Toggle theme">
    🌓
  </button>

  <main>
    <div class="content-container">
      <div class="icon-container">
        <span class="js-icon">⚡️</span>
      </div>
      <h1>JavaScript Required</h1>
      <p class="message">
        You must enable JavaScript to use this website.<br>
        Please enable JavaScript and refresh your browser.
      </p>
      <p class="help-text">
        Need help enabling JavaScript?
      </p>
      <a href="https://enable-javascript.com" target="_blank" rel="noopener noreferrer" class="help-link">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="16" x2="12" y2="12"/>
          <line x1="12" y1="8" x2="12.01" y2="8"/>
        </svg>
        Learn how to enable JavaScript
      </a>
    </div>
  </main>

  <footer>
    <p>
      ©️ 2025 copyright • 
      <a href="https://github.com/dohoudaniel" target="_blank" rel="noopener noreferrer">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
        </svg>
        Daniel Favour Dohou
      </a>
      • All Rights Reserved
    </p>
  </footer>

  <script>
    // Theme Toggle
    const themeToggle = document.querySelector('.theme-toggle');
    
    // Check for saved theme preference or system preference
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme) {
      document.documentElement.setAttribute('data-theme', savedTheme);
    } else if (prefersDark) {
      document.documentElement.setAttribute('data-theme', 'dark');
    }

    // Theme toggle handler
    themeToggle.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    });
  </script>
</body>
</html>