<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Home — URL Shortener (Aetheris)</title>
  <link rel="icon" type="image/png" href="/logo.png">
  <link rel="stylesheet" href="https://unpkg.com/lucide-static@0.321.0/font/lucide.css">
  <style>
    /* Theme Variables */
    :root {
      --background: #ffffff;
      --foreground: #1a1a1a;
      --primary: #4361ee;
      --secondary: #3f37c9;
      --muted: #64748b;
      --border: #e2e8f0;
      --card-bg: #ffffff;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --warning-bg: #fee2e2;
      --warning-text: #dc2626;
      
      /* Gradients */
      --gradient-primary: linear-gradient(135deg, #4361ee, #3f37c9);
      --gradient-hover: linear-gradient(135deg, #3f37c9, #4361ee);
    }

    [data-theme="dark"] {
      --background: #0f172a;
      --foreground: #f8fafc;
      --primary: #60a5fa;
      --secondary: #818cf8;
      --muted: #94a3b8;
      --border: #1e293b;
      --card-bg: #1e293b;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
      --warning-bg: #7f1d1d;
      --warning-text: #fecaca;
      
      --gradient-primary: linear-gradient(135deg, #60a5fa, #818cf8);
      --gradient-hover: linear-gradient(135deg, #818cf8, #60a5fa);
    }

    /* Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      transition: background-color 0.3s, color 0.3s, transform 0.3s;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: var(--background);
      color: var(--foreground);
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    /* NoScript Warning */
    .noscript-warning {
      background-color: var(--warning-bg);
      color: var(--warning-text);
      text-align: center;
      padding: 1rem;
      font-size: 0.875rem;
    }

    /* Main Content */
    main {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 2rem;
      position: relative;
      overflow: hidden;
    }

    /* Background Pattern */
    main::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at top right, 
                  rgba(67, 97, 238, 0.1),
                  transparent 70%);
      pointer-events: none;
    }

    h1 {
      font-size: clamp(2rem, 5vw, 3.5rem);
      font-weight: 700;
      margin-bottom: 1.5rem;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      animation: fadeInDown 0.8s ease-out;
    }

    main p {
      font-size: clamp(1.1rem, 2vw, 1.3rem);
      color: var(--muted);
      max-width: 600px;
      margin-bottom: 2.5rem;
      animation: fadeInUp 0.8s ease-out 0.2s backwards;
    }

    .intro {
      font-size: clamp(1.1rem, 2vw, 1.3rem);
      color: var(--muted);
      max-width: 600px;
      margin-bottom: 2.5rem;
      animation: fadeInUp 0.8s ease-out 0.2s backwards;
    }

    /* Button Container */
    .button-container {
      display: flex;
      gap: 1rem;
      animation: fadeInUp 0.8s ease-out 0.4s backwards;
    }

    /* Button Styles */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.875rem 2rem;
      border-radius: 0.75rem;
      font-size: 1.1rem;
      font-weight: 500;
      text-decoration: none;
      transition: transform 0.2s, box-shadow 0.2s;
      position: relative;
      overflow: hidden;
    }

    .btn-primary {
      background: var(--gradient-primary);
      color: white;
    }

    .btn-secondary {
      background: transparent;
      color: var(--foreground);
      border: 2px solid var(--border);
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    .btn-primary:hover {
      background: var(--gradient-hover);
    }

    .btn-secondary:hover {
      border-color: var(--primary);
      color: var(--primary);
    }

    /* Theme Toggle */
    .theme-toggle {
      position: fixed;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      color: var(--foreground);
      padding: 0.5rem;
      cursor: pointer;
      border-radius: 0.5rem;
      transition: background-color 0.2s;
      z-index: 100;
    }

    .theme-toggle:hover {
      background-color: var(--border);
    }

    /* Footer */
    footer {
      text-align: center;
      padding: 2rem;
      border-top: 1px solid var(--border);
      color: var(--muted);
      font-size: 0.875rem;
      margin-top: auto;
    }

    footer a {
      color: var(--muted);
      text-decoration: none;
      transition: color 0.2s;
    }

    footer a:hover {
      color: var(--primary);
    }

    /* Animations */
    @keyframes fadeInDown {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Responsive Design */
    @media (max-width: 640px) {
      main {
        padding: 1.5rem;
      }

      .button-container {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
      }

      .btn {
        width: 100%;
      }
    }

    @media (prefers-reduced-motion: reduce) {
      * {
        animation: none !important;
        transition: none !important;
      }
    }
  </style>
</head>
<body>
  <noscript>
    <div class="noscript-warning">
      You must enable JavaScript to be able to use the website.
    </div>
  </noscript>

  <button class="theme-toggle" aria-label="Toggle theme">
    🌓
  </button>

  <main>
    <h1>Welcome to Aetheris' URL Shortener</h1>
    <p class="intro">Your one-stop solution to shorten URLs and track clicks. Simple, fast, and reliable.</p>
    <div class="button-container">
      <a href="/login" class="btn btn-primary">
        <i class="lucide-log-in"></i>
        Login
      </a>
      <a href="/signup" class="btn btn-secondary">
        <i class="lucide-user-plus"></i>
        Sign Up
      </a>
    </div>
  </main>

  <footer>
    <p>
      ©️ 2025 copyright • 
      <a href="https://github.com/dohoudaniel" target="_blank" rel="noopener noreferrer">Daniel Favour Dohou</a>
      • All Rights Reserved
    </p>
  </footer>

  <script>
    // Theme Toggle
    const themeToggle = document.querySelector('.theme-toggle');
    
    // Check for saved theme preference or system preference
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme) {
      document.documentElement.setAttribute('data-theme', savedTheme);
    } else if (prefersDark) {
      document.documentElement.setAttribute('data-theme', 'dark');
    }

    // Theme toggle handler
    themeToggle.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    });

    // JavaScript check
    if (!document.cookie.includes('js_enabled=true')) {
      document.cookie = "js_enabled=true; path=/";
      location.reload();
    }
  </script>
</body>
</html>